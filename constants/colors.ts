/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 * There are many other ways to style your app. For example, [Nativewind](https://www.nativewind.dev/), [Tamagui](https://tamagui.dev/), [unistyles](https://reactnativeunistyles.vercel.app), etc.
 */

const tintColorLight = "#0a7ea4";
const tintColorDark = "#fff";

export const Colors = {
   light: {
      text: "#11181C",
      background: "#fff",
      tint: tintColorLight,
      icon: "#687076",
      tabIconDefault: "#687076",
      tabIconSelected: tintColorLight,
      error: "#FF3B30",
      card: "#FAFAFA",
      textMuted: "#687076",
      inputBackground: "#F0F0F0", // Added for light mode
      border: "#D1D1D1", // Added for light mode
   },
   dark: {
      text: "#ECEDEE",
      background: "#151718",
      tint: tintColorDark,
      icon: "#9BA1A6",
      tabIconDefault: "#9BA1A6",
      tabIconSelected: tintColorDark,
      error: "#FF453A",
      card: "#2C2C2E",
      textMuted: "#9BA1A6",
      inputBackground: "#2C2C2E", // Added for dark mode
      border: "#4A4A4C", // Added for dark mode
   },
};

// Theme-aware color palette function for data reading screen
export const getDataReadingColors = (theme: "light" | "dark") => ({
   primary: "#0a7ea4",
   primaryLight: "#818CF8",
   secondary: "#06B6D4",
   success: "#10B981",
   warning: "#F59E0B",
   error: "#EF4444",
   background: theme === "light" ? "#F8FAFC" : "#0F172A",
   cardBackground: theme === "light" ? "#FFFFFF" : "#1E293B",
   textPrimary: theme === "light" ? "#1F2937" : "#F1F5F9",
   textSecondary: theme === "light" ? "#6B7280" : "#94A3B8",
   border: theme === "light" ? "#E5E7EB" : "#334155",
   shadow: theme === "light" ? "rgba(0, 0, 0, 0.1)" : "rgba(0, 0, 0, 0.3)",
   // Measurement card colors - harmonized color scheme
   spo2Card: theme === "light" ? "#DBEAFE" : "#1E3A8A",
   heartRateCard: theme === "light" ? "#DBEAFE" : "#1E3A8A",
   pulseRateCard: theme === "light" ? "#D1F2EB" : "#0D4F3C",
   piCard: theme === "light" ? "#FEF0E6" : "#8B4513",
   // Scale measurement card colors
   weightCard: theme === "light" ? "#E0F2FE" : "#164E63",
   bmiCard: theme === "light" ? "#F0FDF4" : "#14532D",
   bodyFatCard: theme === "light" ? "#FEF3C7" : "#92400E",
   muscleCard: theme === "light" ? "#F3E8FF" : "#6B21A8",
   // Text colors for measurement cards - harmonized
   spo2Text: theme === "light" ? "#1E40AF" : "#93C5FD",
   heartRateText: theme === "light" ? "#1E40AF" : "#93C5FD",
   pulseRateText: theme === "light" ? "#0F5132" : "#7DD3C0",
   piText: theme === "light" ? "#B45309" : "#F4A460",
   // Scale text colors
   weightText: theme === "light" ? "#0C4A6E" : "#7DD3FC",
   bmiText: theme === "light" ? "#15803D" : "#86EFAC",
   bodyFatText: theme === "light" ? "#A16207" : "#FCD34D",
   muscleText: theme === "light" ? "#7C3AED" : "#C4B5FD",
});
