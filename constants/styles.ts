import { Dimensions, Platform, StyleSheet } from "react-native";

import { ThemeColors } from "@/context/ThemeContext";
import { spacing } from "./spacing";
import { Colors } from "./colors";

export const getCommonStyles = (colors: ThemeColors) => {
   const { height } = Dimensions.get("window");

   return StyleSheet.create({
      header: {
         flexDirection: "row",
         justifyContent: "center",
         alignItems: "center",
         paddingVertical: 15,
         paddingHorizontal: 20,
         backgroundColor: colors.background,
      },
      bottomSheet: {
         backgroundColor: colors.inputBackground,
         color: colors.text,
      },
      shadow: {
         shadowColor: "#000",
         shadowOffset: {
            width: 0,
            height: 1,
         },
         shadowOpacity: 0.2,
         shadowRadius: 1.41,
         elevation: 2,
      },
      logo: {
         ...Platform.select({
            web: {
               width: 200,
               height: 80,
            },
            default: {
               width: 100,
               height: 40,
            },
         }),
      },
      modalOverlay: {
         flex: 1,
         flexDirection: "row",
         backgroundColor: "rgba(0,0,0,0.3)",
      },
      formContainer: {
         width: "100%",
         maxWidth: 640,
         alignSelf: "center",
         padding: 10,
      },
      menuContainer: {
         height: height,
         width: 280,
         backgroundColor: colors.background,
         padding: 20,
         paddingTop: 90,
         borderTopRightRadius: 20,
         borderBottomRightRadius: 20,
         shadowColor: "#000",
         shadowOffset: { width: 0, height: 2 },
         shadowOpacity: 0.2,
         shadowRadius: 5,
         elevation: 5,
         justifyContent: "space-between",
         paddingBottom: 60,
      },
      menuItemRow: {
         flexDirection: "row",
         alignItems: "center",
         paddingVertical: 15,
      },
      icon: {
         marginRight: 12,
      },
      menuItem: {
         fontSize: 16,
      },
      spacer: {
         flex: 1,
      },
      profileSection: {
         borderTopWidth: 1,
         borderTopColor: "#ddd",
         paddingTop: 16,
         flexDirection: "row",
         alignItems: "center",
         gap: 12,
      },
      profileIcon: {
         width: 40,
         height: 40,
         borderRadius: 20,
         backgroundColor: "#eee",
         justifyContent: "center",
         alignItems: "center",
      },
      profileInitials: {
         fontSize: 40,
         fontWeight: "bold",
         color: "#333",
      },
      profileName: {
         fontWeight: "600",
         fontSize: 15,
         marginTop: 10,
         color: colors.text,
      },
      profileEmail: {
         fontSize: 12,
      },
      safeArea: {
         flex: 1,
         backgroundColor: colors.background,
      },
      keyboardAvoidingView: {
         flex: 1,
         backgroundColor: colors.background,
      },
      scrollViewContent: {
         flexGrow: 1,
         justifyContent: "center",
         paddingVertical: 20,
      },
      container: {
         flex: 1,
         justifyContent: "center",
         paddingHorizontal: 24,
      },
      logoContainer: {
         alignItems: "center",
         marginBottom: 40,
      },
      title: {
         fontSize: 26,
         color: colors.text,
         textAlign: "center",
         marginBottom: spacing.xl,
      },
      subtitle: {
         fontSize: 16,
         color: colors.icon,
         textAlign: "center",
         marginBottom: 40,
      },
      inputGroup: {
         flexDirection: "row",
         alignItems: "center",
         backgroundColor: colors.inputBackground,
         borderRadius: 12,
         marginBottom: 18,
         paddingHorizontal: 12,
         height: 52,
      },
      inputIcon: {
         marginRight: 10,
      },
      input: {
         flex: 1,
         height: "100%",
         fontSize: 16,
         color: colors.text,
         paddingHorizontal: 10,
      },
      eyeButton: {
         padding: 10,
      },
      forgotPasswordContainer: {
         alignSelf: "flex-end",
         marginBottom: 24,
      },
      forgotPasswordText: {
         fontSize: 14,
         color: Colors.light.tint,
         fontWeight: "500",
      },
      button: {
         backgroundColor: Colors.light.tint,
         paddingVertical: 16,
         borderRadius: 12,
         alignItems: "center",
         justifyContent: "center",
         minHeight: 52,
         opacity: 1,
         shadowColor: "#000",
         shadowOffset: { width: 0, height: 2 },
         shadowOpacity: 0.1,
         shadowRadius: 4,
         elevation: 3,
      },
      buttonPressed: {
         opacity: 0.85,
         transform: [{ scale: 0.98 }],
      },
      buttonDisabled: {
         opacity: 0.6,
         backgroundColor: colors.icon,
         shadowColor: "transparent",
         elevation: 0,
      },
      buttonText: {
         color: "#FFFFFF",
         fontSize: 16,
         fontWeight: "600",
      },
      footer: {
         flexDirection: "row",
         justifyContent: "center",
         marginTop: 32,
         paddingBottom: 10,
      },
      footerText: {
         color: colors.icon,
         fontSize: 14,
      },
      linkText: {
         color: Colors.light.tint,
         fontSize: 14,
         fontWeight: "600",
      },
      errorText: {
         color: Colors.light.error,
         fontSize: 12,
         marginTop: -10,
         marginBottom: 10,
         paddingLeft: 15,
      },
      apiErrorText: {
         textAlign: "center",
         fontWeight: "bold",
         marginBottom: 15,
      },
      iconContainer: {
         alignItems: "center",
         marginBottom: 30,
      },
      profileCircle: {
         width: 100,
         height: 100,
         borderRadius: 50,
         backgroundColor: "#ddd",
         justifyContent: "center",
         alignItems: "center",
         position: "relative",
      },
      cancelButton: {
         backgroundColor: "#A9A9A9",
         paddingVertical: 16,
         borderRadius: 12,
         alignItems: "center",
         justifyContent: "center",
         minHeight: 52,
         shadowColor: "#000",
         shadowOffset: { width: 0, height: 2 },
         shadowOpacity: 0.1,
         shadowRadius: 4,
         elevation: 3,
         marginTop: 12,
      },
      cancelButtonPressed: {
         opacity: 0.85,
         transform: [{ scale: 0.98 }],
      },
      cancelButtonText: {
         color: "#FFFFFF",
         fontSize: 16,
         fontWeight: "600",
      },
      userInfo: {
         fontSize: 16,
         marginBottom: 10,
         color: colors.text,
      },
      tokenInfo: {
         fontSize: 12,
         color: colors.icon,
         marginVertical: 10,
      },
      buttonContainer: {
         marginTop: 30,
      },
      tabBar: {
         flexDirection: "row",
         borderTopWidth: 1,
         height: 70,
         alignItems: "center",
         justifyContent: "space-around",
         paddingBottom: 15,
         paddingTop: 20,
         // marginBottom: 30,
      },
      tab: {
         flex: 1,
         alignItems: "center",
         justifyContent: "center",
      },
      iconLabelWrapper: {
         alignItems: "center",
         justifyContent: "center",
      },
      avatarContainer: {
         alignItems: "center",
         marginTop: 40,
         marginBottom: 32,
      },
      card: {
         marginHorizontal: 24,
         backgroundColor: colors.card,
         borderRadius: 14,
         shadowOpacity: Platform.OS === "android" ? 0.13 : 0.06,
         shadowRadius: 8,
         elevation: 4,
         paddingVertical: 2,
         paddingHorizontal: 0,
      },
      row: {
         flexDirection: "row",
         alignItems: "center",
         paddingVertical: 18,
         paddingHorizontal: 18,
      },
      iconLeft: {
         marginRight: 16,
      },
      rowTitle: {
         fontSize: 17,
         fontWeight: "500",
         color: colors.text,
         flex: 1,
      },
      arrowRight: {
         marginLeft: 6,
      },
      arrowRightFar: {
         marginLeft: 30,
      },
      divider: {
         height: 1,
         backgroundColor: colors.icon + "14",
         marginHorizontal: 10,
      },
      logoutRow: {
         flexDirection: "row",
         alignItems: "center",
         paddingVertical: 18,
         paddingHorizontal: 18,
      },
      logoutText: {
         fontSize: 17,
         fontWeight: "500",
         color: colors.error,
         flex: 1,
      },
   });
};

export const getDataReadingStyles = (colors: any) => {
   return StyleSheet.create({
      // Container/Layout Styles
      scrollView: {
         flex: 1,
         backgroundColor: colors.background,
      },
      scrollViewContent: {
         flexGrow: 1,
      },
      mainContainer: {
         paddingHorizontal: 20,
         flex: 1,
      },

      // Header Styles
      headerContainer: {
         backgroundColor: colors.background,
         paddingTop: 60,
         paddingBottom: 20,
         paddingHorizontal: 20,
      },
      headerRow: {
         flexDirection: "row",
         alignItems: "center",
         marginBottom: 30,
      },
      backButton: {
         backgroundColor: colors.cardBackground,
         borderRadius: 12,
         padding: 8,
         marginRight: 15,
         shadowColor: colors.shadow,
         shadowOffset: { width: 0, height: 2 },
         shadowOpacity: 0.1,
         shadowRadius: 4,
         elevation: 2,
      },
      headerTitle: {
         color: colors.textPrimary,
         fontSize: 28,
         fontWeight: "bold",
         flex: 1,
      },
      headerIcon: {
         fontSize: 32,
      },

      // Card Styles
      deviceStatusCard: {
         backgroundColor: colors.cardBackground,
         borderRadius: 16,
         padding: 20,
         marginBottom: 20,
         shadowColor: colors.shadow,
         shadowOffset: { width: 0, height: 2 },
         shadowOpacity: 0.1,
         shadowRadius: 8,
         elevation: 3,
         flexDirection: "row",
         alignItems: "center",
      },
      statusIcon: {
         backgroundColor: colors.success,
         borderRadius: 20,
         width: 40,
         height: 40,
         justifyContent: "center",
         alignItems: "center",
         marginRight: 16,
      },
      deviceStatusTextContainer: {
         flex: 1,
      },

      // Measurement Status Card
      measurementStatusCard: {
         backgroundColor: colors.cardBackground,
         borderRadius: 16,
         padding: 20,
         marginBottom: 20,
         shadowColor: colors.shadow,
         shadowOffset: { width: 0, height: 2 },
         shadowOpacity: 0.1,
         shadowRadius: 8,
         elevation: 3,
      },
      statusHeader: {
         flexDirection: "row",
         alignItems: "center",
         marginBottom: 20,
      },
      measurementStatusIcon: {
         borderRadius: 20,
         width: 40,
         height: 40,
         justifyContent: "center",
         alignItems: "center",
         marginRight: 16,
      },

      // Embedded Measurement Cards
      measurementCardsContainer: {
         marginTop: 16,
      },
      measurementRow: {
         flexDirection: "row",
         marginBottom: 12,
         gap: 12,
      },
      measurementRowSecond: {
         flexDirection: "row",
         gap: 12,
      },
      measurementCardWrapper: {
         flex: 1,
      },
      measurementCard: {
         borderRadius: 12,
         padding: 16,
         alignItems: "center",
         shadowColor: colors.shadow,
         shadowOffset: { width: 0, height: 1 },
         shadowOpacity: 0.05,
         shadowRadius: 4,
         elevation: 2,
         height: 100,
         justifyContent: "center",
      },

      // Timestamp Card
      timestampCard: {
         backgroundColor: colors.cardBackground,
         borderRadius: 12,
         padding: 16,
         marginBottom: 20,
         shadowColor: colors.shadow,
         shadowOffset: { width: 0, height: 2 },
         shadowOpacity: 0.1,
         shadowRadius: 8,
         elevation: 3,
         flexDirection: "row",
         alignItems: "center",
         justifyContent: "center",
      },

      // Data Submission Card
      dataSubmissionCard: {
         backgroundColor: colors.cardBackground,
         borderRadius: 16,
         padding: 20,
         marginBottom: 20,
         shadowColor: colors.shadow,
         shadowOffset: { width: 0, height: 2 },
         shadowOpacity: 0.1,
         shadowRadius: 8,
         elevation: 3,
      },
      dataSubmissionHeader: {
         flexDirection: "row",
         alignItems: "center",
         marginBottom: 20,
      },
      dataSubmissionIcon: {
         backgroundColor: "#0a7ea4",
         borderRadius: 20,
         width: 40,
         height: 40,
         justifyContent: "center",
         alignItems: "center",
         marginRight: 16,
      },

      // Battery Card
      batteryCard: {
         backgroundColor: colors.cardBackground,
         borderRadius: 16,
         padding: 20,
         marginBottom: 20,
         shadowColor: colors.shadow,
         shadowOffset: { width: 0, height: 2 },
         shadowOpacity: 0.1,
         shadowRadius: 8,
         elevation: 3,
         flexDirection: "row",
         alignItems: "center",
      },
      batteryIcon: {
         backgroundColor: colors.success,
         borderRadius: 12,
         width: 48,
         height: 48,
         justifyContent: "center",
         alignItems: "center",
         marginRight: 16,
      },
      batteryTextContainer: {
         flex: 1,
      },

      // History Card
      historyCard: {
         backgroundColor: colors.cardBackground,
         borderRadius: 16,
         padding: 20,
         marginBottom: 20,
         shadowColor: colors.shadow,
         shadowOffset: { width: 0, height: 2 },
         shadowOpacity: 0.1,
         shadowRadius: 8,
         elevation: 3,
      },
      historyHeader: {
         flexDirection: "row",
         justifyContent: "space-between",
         alignItems: "center",
         marginBottom: 16,
      },
      historyItem: {
         backgroundColor: colors.background,
         borderRadius: 12,
         padding: 16,
         marginBottom: 12,
         shadowColor: colors.shadow,
         shadowOffset: { width: 0, height: 1 },
         shadowOpacity: 0.05,
         shadowRadius: 4,
         elevation: 2,
      },
      historyItemHeader: {
         flexDirection: "row",
         justifyContent: "space-between",
         alignItems: "center",
         marginBottom: 8,
      },

      // Text Styles
      deviceStatusTitle: {
         fontSize: 18,
         fontWeight: "600",
         color: colors.textPrimary,
         marginBottom: 4,
      },
      deviceStatusSubtitle: {
         fontSize: 14,
         color: colors.textSecondary,
         fontWeight: "500",
      },
      statusTitle: {
         fontSize: 18,
         fontWeight: "600",
         color: colors.textPrimary,
      },
      measurementCardLabel: {
         fontSize: 12,
         fontWeight: "600",
         marginBottom: 4,
         marginTop: 8,
      },
      measurementCardValue: {
         fontSize: 24,
         fontWeight: "bold",
         marginBottom: 8,
      },
      measurementCardValueCentered: {
         fontSize: 24,
         fontWeight: "bold",
         marginBottom: 2,
      },
      measurementCardUnit: {
         fontSize: 10,
         fontWeight: "500",
         opacity: 0.8,
      },
      measurementCardUnitHidden: {
         fontSize: 10,
         fontWeight: "500",
         opacity: 0,
      },
      timestampIcon: {
         fontSize: 20,
         marginRight: 8,
      },
      timestampText: {
         fontSize: 16,
         color: colors.textPrimary,
         fontWeight: "500",
      },
      dataSubmissionTitle: {
         fontSize: 18,
         fontWeight: "600",
         color: colors.textPrimary,
      },
      notesLabel: {
         fontSize: 14,
         fontWeight: "600",
         color: colors.textPrimary,
         marginBottom: 8,
      },
      notesHelper: {
         fontSize: 12,
         color: colors.textSecondary,
         marginTop: 8,
      },
      successMessage: {
         fontSize: 14,
         color: colors.success,
         fontWeight: "600",
         marginLeft: 8,
      },
      batteryTitle: {
         fontSize: 16,
         fontWeight: "600",
         color: colors.textPrimary,
         marginBottom: 4,
      },
      batteryLevel: {
         fontSize: 24,
         fontWeight: "bold",
         color: colors.textPrimary,
      },
      batteryLastUpdated: {
         fontSize: 12,
         color: colors.textSecondary,
         marginTop: 4,
      },
      historyTitle: {
         fontSize: 18,
         fontWeight: "600",
         color: colors.textPrimary,
      },
      historyBadge: {
         color: "white",
         fontSize: 12,
         fontWeight: "600",
      },
      historyItemValue: {
         fontSize: 16,
         fontWeight: "600",
         color: colors.textPrimary,
      },
      historyItemTimestamp: {
         fontSize: 12,
         color: colors.textSecondary,
         marginTop: 4,
      },

      // Button Styles
      primaryButton: {
         backgroundColor: colors.primary,
         borderRadius: 16,
         padding: 16,
         marginBottom: 12,
         shadowColor: colors.shadow,
         shadowOffset: { width: 0, height: 2 },
         shadowOpacity: 0.1,
         shadowRadius: 8,
         elevation: 3,
         flexDirection: "row",
         alignItems: "center",
         justifyContent: "center",
      },
      secondaryButton: {
         backgroundColor: colors.cardBackground,
         borderRadius: 16,
         padding: 16,
         marginBottom: 12,
         shadowColor: colors.shadow,
         shadowOffset: { width: 0, height: 2 },
         shadowOpacity: 0.1,
         shadowRadius: 8,
         elevation: 3,
         flexDirection: "row",
         alignItems: "center",
         justifyContent: "center",
      },
      buttonText: {
         color: "white",
         fontSize: 16,
         fontWeight: "600",
      },
      secondaryButtonText: {
         fontSize: 16,
         fontWeight: "600",
         color: colors.textPrimary,
      },
      historyBadgeButton: {
         backgroundColor: colors.primary,
         borderRadius: 12,
         paddingHorizontal: 12,
         paddingVertical: 4,
      },

      // Input Styles
      notesInputContainer: {
         marginBottom: 20,
      },
      notesInput: {
         backgroundColor: colors.background,
         borderRadius: 12,
         padding: 16,
         fontSize: 16,
         color: colors.textPrimary,
         borderWidth: 1,
         borderColor: colors.border,
         minHeight: 80,
         textAlignVertical: "top",
      },

      // Icon Styles
      iconMarginRight: {
         marginRight: 8,
      },

      // Submit Button Styles
      submitButtonContainer: {
         // backgroundColor: colors.primary,
         borderRadius: 16,
         padding: 18,
         marginBottom: 12,
         shadowColor: colors.shadow,
         shadowOffset: { width: 0, height: 2 },
         shadowOpacity: 0.1,
         shadowRadius: 8,
         elevation: 3,
         flexDirection: "row",
         alignItems: "center",
         justifyContent: "center",
      },
      submitButtonContainerSuccess: {
         backgroundColor: colors.success,
      },
      submitButtonText: {
         color: "white",
         fontSize: 16,
         fontWeight: "600",
      },
      successIndicatorContainer: {
         flexDirection: "row",
         alignItems: "center",
         justifyContent: "center",
         marginTop: 12,
      },
      successIndicatorText: {
         fontSize: 14,
         color: colors.success,
         fontWeight: "600",
         marginLeft: 8,
      },

      // Battery Card Styles (specific)
      batteryIconContainer: {
         backgroundColor: colors.success,
         borderRadius: 12,
         width: 48,
         height: 48,
         justifyContent: "center",
         alignItems: "center",
         marginRight: 16,
      },

      // Control Buttons Container
      controlButtonsContainer: {
         marginBottom: 20,
      },

      // Primary Action Button (dynamic styles handled in component)
      primaryActionButton: {
         backgroundColor: colors.primary,
         borderRadius: 16,
         padding: 16,
         marginBottom: 12,
         shadowColor: colors.shadow,
         shadowOffset: { width: 0, height: 2 },
         shadowOpacity: 0.1,
         shadowRadius: 8,
         elevation: 3,
         flexDirection: "row",
         alignItems: "center",
         justifyContent: "center",
      },
      primaryActionButtonText: {
         color: "white",
         fontSize: 16,
         fontWeight: "600",
      },

      // Secondary Action Button
      secondaryActionButton: {
         backgroundColor: colors.cardBackground,
         borderRadius: 16,
         padding: 16,
         marginBottom: 12,
         shadowColor: colors.shadow,
         shadowOffset: { width: 0, height: 2 },
         shadowOpacity: 0.1,
         shadowRadius: 8,
         elevation: 3,
         flexDirection: "row",
         alignItems: "center",
         justifyContent: "center",
      },
      secondaryActionButtonText: {
         fontSize: 16,
         fontWeight: "600",
         color: colors.textPrimary,
      },

      // History Badge
      historyBadgeContainer: {
         backgroundColor: colors.primary,
         borderRadius: 12,
         paddingHorizontal: 12,
         paddingVertical: 4,
      },

      // History Item Styles
      historyItemContainer: {
         backgroundColor: colors.background,
         borderRadius: 12,
         padding: 16,
         marginBottom: 12,
         shadowColor: colors.shadow,
         shadowOffset: { width: 0, height: 1 },
         shadowOpacity: 0.05,
         shadowRadius: 4,
         elevation: 2,
      },
      historyItemRow: {
         flexDirection: "row",
         justifyContent: "space-between",
         alignItems: "center",
         marginBottom: 8,
      },
      historyItemValueText: {
         fontSize: 16,
         fontWeight: "600",
         color: colors.textPrimary,
      },
      historyItemTimestampText: {
         fontSize: 12,
         color: colors.textSecondary,
         marginTop: 4,
      },

      // Utility Styles
      marginBottom20: {
         marginBottom: 20,
      },
      flex1: {
         flex: 1,
      },
      iconMarginRight8: {
         marginRight: 8,
      },
   });
};

export const styles = StyleSheet.create({
   container: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      padding: 20,
   },
   deviceItem: {
      padding: 15,
      borderBottomWidth: 1,
      borderBottomColor: "#eee",
      width: "100%",
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: "#ffffff",
   },
   connectButton: {
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 6,
      backgroundColor: "#007bff",
      alignItems: "center",
      justifyContent: "center",
      minWidth: 80,
   },
   listHeader: {
      fontWeight: "bold",
      fontSize: 18,
      paddingVertical: 10,
      textAlign: "center",
   },
});
