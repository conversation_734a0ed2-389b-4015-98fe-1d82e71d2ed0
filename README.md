# AdaptIT - Frontend FHIR Mobile MVP

A mobile application built with React Native and Expo, focused on implementing FHIR (Fast Healthcare Interoperability Resources) standards for healthcare data exchange.

## Project Overview

This application serves as a mobile MVP (Minimum Viable Product) for interacting with FHIR-based healthcare APIs. It provides a foundation for building healthcare applications that can securely access and display patient data in compliance with healthcare interoperability standards.

There are some note-worthy facts to keep in mind which can be helpful when working on the project.

1. **iHealth SDK Build Errors**: iHealth SDK uses an outdated build configuration that prevents some Java classes from compiling. To workaround this, a patch has been introduced to update the build config to a state where the library can now be built successfully without any errors. The patch is automatically applied by a post-install script every time packages are installed through npm.

2. **License**: A valid license is **strictly mandatory** for connecting to iHealth devices. Without it, the app will not be able to authenticate itself.

3. **Plugins**: The application leverages expo config plugins to inject essential changes at the native level of the application. This includes requesting the necessary permissions and configuring the required settings for ensuring a successful deployment for both platforms.

## Features

- React Native with Expo framework
- Navigation using Expo Router with file-based routing
- Cross-platform support (iOS and Android)
- Theming capabilities with light/dark mode support
- UI components optimized for healthcare data display

## Development Setup

### Prerequisites

- [Node.js](https://nodejs.org/) (LTS version recommended)
- [npm](https://www.npmjs.com/) or [yarn](https://yarnpkg.com/)
- [Expo CLI](https://docs.expo.dev/workflow/expo-cli/)
- iOS development requires macOS with Xcode installed
- Android development requires Android Studio with an emulator configured
- An iHealth SDK license for pairing with iHealth devices

### Setup Guide

After cloning the repository, follow these steps to properly set up the development environment:

#### 1. Install dependencies

```bash
npm install
```

#### 2. iOS Setup (macOS only)

```bash
cd ios
pod install
cd ..
```

> **Note**: iOS development requires a Mac. If you don't have a Mac, you can still work on Android development.

#### 3. Android Setup

- Make sure your PATH variable contains the path to your Android SDK.

   OR

- Create a `local.properties` file in the `android` directory
- Add the path to your Android SDK:
   - For macOS: `sdk.dir=/Users/<USER>/Library/Android/sdk`
   - For Windows: `sdk.dir=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk`
   - For Linux: `sdk.dir=/home/<USER>/Android/Sdk`

> **Note**: Replace USERNAME with your actual username.

#### 4. Environment Configuration

Create a file called `.env` in the root directory of the project. There should be one variable containing the URL to your backend.

```env
EXPO_PUBLIC_API_URL=BACKEND_URL
```

> **Note**: Replace BACKEND_URL with the actual URL to your backend.

### Installation

1. Clone the repository

   ```bash
   git clone <repository-url>
   cd fhir-mobile-mvp
   ```

2. Install dependencies

   ```bash
   npm install
   ```

3. Perform prebuild

   ```bash
   npx expo prebuild --clean
   ```

4. Start the development server.
   ```bash
   npx expo start
   ```

> **Note**: It is recommended to explicitly set the port of your frontend application to avoid any port conflicts.

### Running on Devices

- **iOS Simulator**: Press `i` in the terminal or click on "Run on iOS simulator" in the Expo Developer Tools
- **Android Emulator**: Press `a` in the terminal or click on "Run on Android device/emulator" in the Expo Developer Tools
- **Physical Device**: Scan the QR code with the Expo Go app (limited functionality) or create a development build

### Troubleshooting

#### Android Build Issues

- Make sure your `local.properties` file contains the correct Android SDK path
- Run `cd android && ./gradlew clean` to clean the Android build

#### iOS Build Issues

- Make sure you've run `pod install` in the iOS directory
- Check that Xcode is properly installed and updated
- Run `cd ios && pod update` if you encounter CocoaPods dependency issues

## Building for Production

### Android

```bash
eas build -p android
```

### iOS

```bash
eas build -p ios
```

## Project Structure

- `/app` - Main application code with file-based routing
- `/components` - Reusable UI components
- `/constants` - Application constants and configuration
- `/hooks` - Custom React hooks
- `/assets` - Static assets like images and fonts

---
