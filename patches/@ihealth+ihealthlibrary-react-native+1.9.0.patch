diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/ReactNativeIOSLibrary.podspec b/node_modules/@ihealth/ihealthlibrary-react-native/ReactNativeIOSLibrary.podspec
index f5b9ede..7903819 100644
--- a/node_modules/@ihealth/ihealthlibrary-react-native/ReactNativeIOSLibrary.podspec
+++ b/node_modules/@ihealth/ihealthlibrary-react-native/ReactNativeIOSLibrary.podspec
@@ -15,6 +15,8 @@ Pod::Spec.new do |s|
   s.source_files = "ios/**/*.{h,m}"
   s.public_header_files = "ios/ReactNativeIOSLibrary/Communication_SDK/Headers/*.h"
   s.vendored_libraries  = "ios/ReactNativeIOSLibrary/Communication_SDK/libiHealthSDK2.14.0.a"
+  s.library = 'c++'
+  s.frameworks = 'CoreBluetooth', 'Foundation', 'UIKit'
   s.requires_arc = true
 
   s.dependency 'React-Core'
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/ios/ReactNativeIOSLibrary/BPProfileModule.h b/node_modules/@ihealth/ihealthlibrary-react-native/ios/ReactNativeIOSLibrary/BPProfileModule.h
index b4fc0d0..3188ccb 100755
--- a/node_modules/@ihealth/ihealthlibrary-react-native/ios/ReactNativeIOSLibrary/BPProfileModule.h
+++ b/node_modules/@ihealth/ihealthlibrary-react-native/ios/ReactNativeIOSLibrary/BPProfileModule.h
@@ -32,6 +32,7 @@
 #define kACTION_SET_ANGLE_SUCCESS_BP @"set_angle_success"
 #define kACTION_INTERRUPTED_BP @"interrupted_bp"
 #define kACTION_Delete_BP @"delete_bp"
+#define kACTION_Delete_BP5S @"delete_bp5s"
 
 
 #define kACTION_ENABLE_OFFLINE_BP @"enable_offline_bp"
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/ios/ReactNativeIOSLibrary/Communication_SDK/libiHealthSDK2.14.0.a b/node_modules/@ihealth/ihealthlibrary-react-native/ios/ReactNativeIOSLibrary/Communication_SDK/libiHealthSDK2.14.0.a
new file mode 100644
index 0000000..2f75e22
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/ios/ReactNativeIOSLibrary/Communication_SDK/libiHealthSDK2.14.0.a differ
