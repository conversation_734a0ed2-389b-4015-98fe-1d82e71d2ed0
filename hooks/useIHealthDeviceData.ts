import { useState } from "react";
import {
   usePO3Measurement,
   UsePO3MeasurementReturn,
} from "./devices/usePO3Measurement";
import {
   useHS2SMeasurement,
   UseHS2SMeasurementReturn,
} from "./devices/useHS2SMeasurement";

export interface DeviceMeasurementData {
   // Common fields for all devices
   timestamp: string;
   deviceMac: string;
   deviceType: string;

   // Pulse Oximeter specific
   spo2?: number;
   heartRate?: number;
   pulseRate?: number;
   pi?: number; // Perfusion Index

   // Scale specific
   weight?: number;
   unit?: string;
   bmi?: number;
   bodyFat?: number;
   muscle?: number;
   bone?: number;
   water?: number;

   // Blood pressure specific
   systolic?: number;
   diastolic?: number;
   meanArterialPressure?: number;

   // Battery info (common)
   batteryLevel?: number;
}

export interface DeviceBatteryData {
   batteryLevel: number;
   deviceMac: string;
   deviceType: string;
   timestamp: string;
}

export type DeviceMeasurementStatus =
   | "idle"
   | "measuring"
   | "completed"
   | "failed"
   | "timeout";

export interface UseIHealthDeviceDataReturn {
   // Measurement state
   measurementStatus: DeviceMeasurementStatus;
   currentMeasurement: DeviceMeasurementData | null;
   measurementHistory: DeviceMeasurementData[];

   // Battery state
   batteryLevel: number | null;
   batteryLastUpdated: string | null;

   // Actions
   startMeasurement: (deviceMac: string, deviceType: string) => void;
   stopMeasurement: (deviceType: string) => void;
   getBatteryLevel: (deviceMac: string, deviceType: string) => void;
   getHistoryData: (deviceMac: string, deviceType: string) => void;
   clearMeasurementHistory: () => void;
}

export const useIHealthDeviceData = (): UseIHealthDeviceDataReturn => {
   // Use device-specific hooks
   const po3Data = usePO3Measurement();
   const hs2sData = useHS2SMeasurement();

   // Track current device type for routing
   const [currentDeviceType, setCurrentDeviceType] = useState<string | null>(
      null,
   );

   const deviceRegistry: Record<
      string,
      UseHS2SMeasurementReturn | UsePO3MeasurementReturn
   > = {
      PO3: po3Data,
      "HS2S Pro": hs2sData,
   };

   // Generic state that routes to appropriate device-specific hook
   const getCurrentDeviceData = () => {
      const device = currentDeviceType
         ? deviceRegistry[currentDeviceType]
         : null;

      if (device) {
         const {
            measurementStatus,
            currentMeasurement,
            measurementHistory,
            batteryLevel,
            batteryLastUpdated,
         } = device;

         return {
            measurementStatus,
            currentMeasurement,
            measurementHistory,
            batteryLevel,
            batteryLastUpdated,
         };
      }

      return {
         measurementStatus: "idle" as DeviceMeasurementStatus,
         currentMeasurement: null,
         measurementHistory: [],
         batteryLevel: null,
         batteryLastUpdated: null,
      };
   };

   const deviceData = getCurrentDeviceData();

   const startMeasurement = (deviceMac: string, deviceType: string) => {
      console.log(
         `ROUTING: Starting measurement for ${deviceType} device: ${deviceMac}`,
      );
      setCurrentDeviceType(deviceType);
      const device = deviceRegistry[deviceType];

      if (device) {
         device.startMeasurement(deviceMac);
      } else {
         console.error(`ROUTING ERROR: Unsupported device type: ${deviceType}`);
      }
   };

   const stopMeasurement = (deviceType: string) => {
      console.log(`ROUTING: Stopping measurement for ${deviceType}`);

      const device = deviceRegistry[deviceType];
      if (device) {
         device.stopMeasurement();
      } else {
         console.error(`ROUTING ERROR: Unsupported device type: ${deviceType}`);
      }
   };

   const getBatteryLevel = (deviceMac: string, deviceType: string) => {
      console.log(
         `ROUTING: Getting battery level for ${deviceType} device: ${deviceMac}`,
      );
      const device = deviceRegistry[deviceType];

      if (device) {
         device.getBatteryLevel(deviceMac);
      } else {
         console.error(`ROUTING ERROR: Unsupported device type: ${deviceType}`);
      }
   };

   const getHistoryData = (deviceMac: string, deviceType: string) => {
      console.log(
         `ROUTING: Getting history data for ${deviceType} device: ${deviceMac}`,
      );
      const device = deviceRegistry[deviceType];

      if (device) {
         device.getHistoryData(deviceMac);
      } else {
         console.error(`ROUTING ERROR: Unsupported device type: ${deviceType}`);
      }
   };

   const clearMeasurementHistory = () => {
      if (!currentDeviceType) return;
      console.log(
         `ROUTING: Clearing measurement history for ${currentDeviceType}`,
      );
      const device = deviceRegistry[currentDeviceType];

      if (device) {
         device.clearMeasurementHistory();
      } else {
         console.error(
            `ROUTING ERROR: Unsupported device type: ${currentDeviceType}`,
         );
      }
   };

   return {
      measurementStatus: deviceData.measurementStatus,
      currentMeasurement: deviceData.currentMeasurement,
      measurementHistory: deviceData.measurementHistory,
      batteryLevel: deviceData.batteryLevel,
      batteryLastUpdated: deviceData.batteryLastUpdated,
      startMeasurement,
      stopMeasurement,
      getBatteryLevel,
      getHistoryData,
      clearMeasurementHistory,
   };
};
