import AsyncStorage from "@react-native-async-storage/async-storage";

const SAVED_DEVICES_KEY = "savedBluetoothDevices";

export interface SavedBluetoothDevice {
   mac: string;
   name: string;
   type: string;
}

export const saveDevice = async (
   device: SavedBluetoothDevice,
): Promise<void> => {
   try {
      const stored = await AsyncStorage.getItem(SAVED_DEVICES_KEY);
      const existing: SavedBluetoothDevice[] = stored ? JSON.parse(stored) : [];

      const updated = [...existing.filter((d) => d.mac !== device.mac), device];

      await AsyncStorage.setItem(SAVED_DEVICES_KEY, JSON.stringify(updated));
      console.log("✅ Device saved to AsyncStorage:", device);
   } catch (e) {
      console.error("❌ Error saving device:", e);
   }
};

export const getSavedDevices = async (): Promise<SavedBluetoothDevice[]> => {
   try {
      const stored = await AsyncStorage.getItem(SAVED_DEVICES_KEY);
      const parsed = stored ? JSON.parse(stored) : [];
      console.log("📥 Retrieved saved devices:", parsed);
      return parsed;
   } catch (e) {
      console.error("❌ Error loading saved devices:", e);
      return [];
   }
};

export const removeDevice = async (mac: string): Promise<void> => {
   try {
      const stored = await AsyncStorage.getItem(SAVED_DEVICES_KEY);
      if (!stored) return;

      const updated = JSON.parse(stored).filter(
         (d: SavedBluetoothDevice) => d.mac !== mac,
      );
      await AsyncStorage.setItem(SAVED_DEVICES_KEY, JSON.stringify(updated));
      console.log(`🗑 Removed device with MAC: ${mac}`);
   } catch (e) {
      console.error("❌ Error removing device:", e);
   }
};
