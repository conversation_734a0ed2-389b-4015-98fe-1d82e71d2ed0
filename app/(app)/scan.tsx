import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ie<PERSON>, SafeAreaView, View, Pressable } from "react-native";
import { useRouter } from "expo-router";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { useIHealth } from "@/hooks/useIHealth";
import { IHealthDevice } from "@/hooks/useIHealthScan";
import { DeviceSelectionCard } from "@/components/ui/DeviceSelectionCard";
import { DeviceDiscoveryModal } from "@/components/ui/DeviceDiscoveryModal";
import {
   SUPPORTED_DEVICE_TYPES,
   DeviceTypeConfig,
   getDeviceTypeBySDKType,
} from "@/constants/deviceTypes";
import {
   saveDevice,
   getSavedDevices,
   removeDevice,
   SavedBluetoothDevice,
} from "@/utils/bluetooth-storage";

export default function ScanScreen() {
   const router = useRouter();
   const [discoveryModalVisible, setDiscoveryModalVisible] = useState(false);
   const [selectedDeviceType, setSelectedDeviceType] =
      useState<DeviceTypeConfig | null>(null);
   const [savedDevices, setSavedDevices] = useState<SavedBluetoothDevice[]>([]);

   const {
      isAuthenticated,
      isScanning,
      discoveredDevices,
      currentScanDeviceType,
      startDeviceTypeScan,
      stopScan,
      connectDevice,
      disconnectDevice,
      connectedDevice,
   } = useIHealth();

   const loadSavedDevices = async () => {
      const devices = await getSavedDevices();
      setSavedDevices(devices);
   };

   useEffect(() => {
      loadSavedDevices();
   }, []);

   useEffect(() => {
      return () => {
         if (connectedDevice) {
            disconnectDevice();
            console.log(
               "Device disconnected on screen unmount:",
               connectedDevice.mac,
            );
         }
      };
   }, [connectedDevice]);

   useEffect(() => {
      return () => {
         console.log(" Cleaning up on screen exit...");
         disconnectDevice(); // This now also covers connectingDeviceRef!
      };
   }, []);

   const handleDeviceTypeSelect = (deviceConfig: DeviceTypeConfig) => {
      if (!isAuthenticated) {
         Alert.alert(
            "Authentication Required",
            "Please wait for authentication to complete before scanning for devices.",
         );
         return;
      }

      setSelectedDeviceType(deviceConfig);
      setDiscoveryModalVisible(true);
      startDeviceTypeScan(deviceConfig.sdkDeviceType);
   };

   const handleDeviceSelect = (device: IHealthDevice) => {
      setDiscoveryModalVisible(false);
      stopScan();

      if (connectedDevice) disconnectDevice();

      const deviceDisplayName =
         device.name || `${device.type || "Unknown"} Device`;

      Alert.alert(
         "Save Device?",
         `Do you want to save "${deviceDisplayName}" for quick access?`,
         [
            {
               text: "No",
               style: "cancel",
               onPress: () => {
                  router.push({
                     pathname: "/data-reading",
                     params: {
                        deviceMac: device.mac,
                        deviceType: device.type,
                        deviceName: deviceDisplayName,
                     },
                  });
               },
            },
            {
               text: "Yes",
               onPress: async () => {
                  await saveDevice({
                     mac: device.mac,
                     name: deviceDisplayName,
                     type: device.type,
                  });
                  await loadSavedDevices();
                  router.push({
                     pathname: "/data-reading",
                     params: {
                        deviceMac: device.mac,
                        deviceType: device.type,
                        deviceName: deviceDisplayName,
                     },
                  });
               },
            },
         ],
      );
   };

   const handleSavedDevicePress = (device: SavedBluetoothDevice) => {
      if (connectedDevice) disconnectDevice();

      router.push({
         pathname: "/data-reading",
         params: {
            deviceMac: device.mac,
            deviceType: device.type,
            deviceName: device.name,
         },
      });
   };

   const handleForgetDevice = async (mac: string) => {
      Alert.alert(
         "Forget Device",
         "Are you sure you want to remove this device?",
         [
            { text: "Cancel", style: "cancel" },
            {
               text: "Forget",
               style: "destructive",
               onPress: async () => {
                  await removeDevice(mac);
                  await loadSavedDevices();
               },
            },
         ],
      );
   };

   const handleCloseDiscoveryModal = () => {
      setDiscoveryModalVisible(false);
      stopScan();
      setSelectedDeviceType(null);
   };

   const handleStopScan = () => {
      stopScan();
   };

   return (
      <SafeAreaView style={{ flex: 1 }}>
         <ThemedView style={{ flex: 1 }}>
            <ScrollView
               contentContainerStyle={{ flexGrow: 1 }}
               showsVerticalScrollIndicator={false}
            >
               {/* 🔒 Saved Devices */}
               {savedDevices.length > 0 && (
                  <View style={{ paddingHorizontal: 20, paddingTop: 20 }}>
                     <ThemedText
                        style={{
                           fontSize: 20,
                           fontWeight: "bold",
                           marginBottom: 16,
                           textAlign: "center",
                        }}
                     >
                        Saved Devices
                     </ThemedText>

                     {savedDevices.map((device) => {
                        const deviceTypeConfig = getDeviceTypeBySDKType(
                           device.type,
                        ) ?? {
                           id: "unknown",
                           name: device.name,
                           description: "Saved Bluetooth Device",
                           icon: "",
                           sdkDeviceType: device.type,
                           color: "#ccc",
                           category: "scale",
                        };

                        return (
                           <View key={device.mac} style={{ marginBottom: 10 }}>
                              <DeviceSelectionCard
                                 deviceConfig={deviceTypeConfig}
                                 onPress={() => handleSavedDevicePress(device)}
                                 isScanning={false}
                                 disabled={false}
                                 savedDeviceName={device.name}
                                 isSaved={true}
                              />
                              <Pressable
                                 onPress={() => handleForgetDevice(device.mac)}
                                 style={{
                                    alignSelf: "center",
                                    marginTop: 4,
                                    marginBottom: 8,
                                    paddingHorizontal: 12,
                                    paddingVertical: 6,
                                    backgroundColor: "#0a7ea4",
                                    borderRadius: 8,
                                 }}
                              >
                                 <ThemedText
                                    style={{ color: "white", fontSize: 12 }}
                                 >
                                    Forget Device
                                 </ThemedText>
                              </Pressable>
                           </View>
                        );
                     })}
                  </View>
               )}

               {/* 🔎 Available Devices */}
               <View style={{ paddingHorizontal: 20, paddingVertical: 20 }}>
                  <ThemedText
                     style={{
                        fontSize: 20,
                        fontWeight: "bold",
                        marginBottom: 16,
                        textAlign: "center",
                     }}
                  >
                     Available Device Types
                  </ThemedText>

                  {SUPPORTED_DEVICE_TYPES.map((deviceConfig) => (
                     <DeviceSelectionCard
                        key={deviceConfig.id}
                        deviceConfig={deviceConfig}
                        onPress={() => handleDeviceTypeSelect(deviceConfig)}
                        isScanning={
                           isScanning &&
                           currentScanDeviceType === deviceConfig.sdkDeviceType
                        }
                        disabled={!isAuthenticated}
                     />
                  ))}
               </View>
            </ScrollView>
         </ThemedView>

         {/* Scanning Modal */}
         <DeviceDiscoveryModal
            visible={discoveryModalVisible}
            onClose={handleCloseDiscoveryModal}
            discoveredDevices={discoveredDevices}
            isScanning={isScanning}
            scanningDeviceType={currentScanDeviceType}
            onDeviceSelect={handleDeviceSelect}
            onStopScan={handleStopScan}
         />
      </SafeAreaView>
   );
}
